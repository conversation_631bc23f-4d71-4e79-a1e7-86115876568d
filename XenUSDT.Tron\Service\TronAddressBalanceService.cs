using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using XenUSDT.Core.DBModel;
using XenUSDT.Core.Service;

namespace XenUSDT.Tron.Service
{
    public class TronAddressBalanceService
    {
        private const string USDT_CONTRACT_ADDRESS = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t";

        /// <summary>
        /// 查询并更新所有地址的余额和多签状态
        /// </summary>
        public static void QueryAndUpdateAddressBalances()
        {
            try
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 开始查询地址余额和多签状态");

                // 从数据库获取所有地址
                var addresses = FreeSQLService.FreeSql.Select<Address>().ToList();
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 共找到 {addresses.Count} 个地址需要查询");

                int processedCount = 0;
                int successCount = 0;
                int errorCount = 0;

                foreach (var address in addresses)
                {
                    try
                    {
                        processedCount++;
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 正在查询地址 {processedCount}/{addresses.Count}: {address.address}");

                        // 调用TRON API查询账户信息
                        var apiUrl = $"https://api.trongrid.io/v1/accounts/{address.address}";
                        var responseJson = TronApiService.Get(apiUrl);
                        
                        // 解析API响应
                        var accountInfo = ParseAccountResponse(responseJson, address.address);
                        
                        if (accountInfo != null)
                        {
                            // 更新数据库
                            var updateResult = FreeSQLService.FreeSql.Update<Address>()
                                .Set(a => a.trx, accountInfo.TrxBalance)
                                .Set(a => a.usdt, accountInfo.UsdtBalance)
                                .Set(a => a.IsMultiSign, accountInfo.IsMultiSign ? true : false)
                                .Where(a => a.id == address.id)
                                .ExecuteAffrows();

                            if (updateResult > 0)
                            {
                                successCount++;
                                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 更新成功: TRX={accountInfo.TrxBalance}, USDT={accountInfo.UsdtBalance}, 多签={accountInfo.IsMultiSign}");
                            }
                            else
                            {
                                errorCount++;
                                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 数据库更新失败");
                            }
                        }
                        else
                        {
                            errorCount++;
                            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 解析账户信息失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 查询地址 {address.address} 时发生错误: {ex.Message}");
                    }
                }

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 查询完成! 总计: {processedCount}, 成功: {successCount}, 失败: {errorCount}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 查询过程发生严重错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析TRON API响应
        /// </summary>
        private static AccountInfo ParseAccountResponse(string responseJson, string queryAddress)
        {
            try
            {
                var response = JsonConvert.DeserializeObject<dynamic>(responseJson);
                
                if (response?.success != true || response?.data == null || response.data.Count == 0)
                {
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] API响应无效或账户不存在");
                    return new AccountInfo { TrxBalance = 0, UsdtBalance = 0, IsMultiSign = false };
                }

                var accountData = response.data[0];
                
                // 解析TRX余额
                decimal trxBalance = 0;
                if (accountData.balance != null)
                {
                    trxBalance = (decimal)accountData.balance;
                }

                // 解析USDT余额
                decimal usdtBalance = 0;
                if (accountData.trc20 != null && accountData.trc20[USDT_CONTRACT_ADDRESS] != null)
                {
                    usdtBalance = (decimal)accountData.trc20[USDT_CONTRACT_ADDRESS];
                }

                // 检查多签状态
                bool isMultiSign = CheckMultiSignature(accountData, queryAddress);

                return new AccountInfo
                {
                    TrxBalance = trxBalance,
                    UsdtBalance = usdtBalance,
                    IsMultiSign = isMultiSign
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 解析API响应时发生错误: {ex.Message}");
                return new AccountInfo { TrxBalance = 0, UsdtBalance = 0, IsMultiSign = false };
            }
        }

        /// <summary>
        /// 检查多签状态
        /// </summary>
        private static bool CheckMultiSignature(dynamic accountData, string queryAddress)
        {
            try
            {
                bool hasOwnerPermission = false;
                bool hasActivePermission = false;
                bool ownerThresholdGreaterThanOne = false;
                bool activeThresholdGreaterThanOne = false;

                // 检查owner_permission
                if (accountData.owner_permission != null)
                {
                    // 检查阈值
                    if (accountData.owner_permission.threshold != null && (int)accountData.owner_permission.threshold > 1)
                    {
                        ownerThresholdGreaterThanOne = true;
                    }

                    // 检查当前地址是否在keys中
                    if (accountData.owner_permission.keys != null)
                    {
                        foreach (var key in accountData.owner_permission.keys)
                        {
                            if (key.address != null && (string)key.address == queryAddress)
                            {
                                hasOwnerPermission = true;
                                break;
                            }
                        }
                    }
                }

                // 检查active_permission
                if (accountData.active_permission != null && accountData.active_permission.Count > 0)
                {
                    foreach (var permission in accountData.active_permission)
                    {
                        // 检查阈值
                        if (permission.threshold != null && (int)permission.threshold > 1)
                        {
                            activeThresholdGreaterThanOne = true;
                        }

                        // 检查当前地址是否在keys中
                        if (permission.keys != null)
                        {
                            foreach (var key in permission.keys)
                            {
                                if (key.address != null && (string)key.address == queryAddress)
                                {
                                    hasActivePermission = true;
                                    break;
                                }
                            }
                        }
                    }
                }

                // 多签条件：当前地址不在权限列表中 OR 阈值大于1
                bool isMultiSign = (!hasOwnerPermission && !hasActivePermission) || ownerThresholdGreaterThanOne || activeThresholdGreaterThanOne;

                return isMultiSign;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 检查多签状态时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 账户信息类
        /// </summary>
        private class AccountInfo
        {
            public decimal TrxBalance { get; set; }
            public decimal UsdtBalance { get; set; }
            public bool IsMultiSign { get; set; }
        }
    }
}
