﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Telegram.Bot;
using Telegram.Bot.Types;

namespace XenUSDT.Core.Service
{
    public class TelegramBotService
    {
        public static string BotToken = "8300656583:AAGNP-bQKz72-wItjxJUq-xCYvMygvVLTUM";
        public static string UserID = "7872412721";

        public static TelegramBotClient TelegramBotClient { get; set; }

        public TelegramBotService()
        {
            try
            {
                TelegramBotClient = new TelegramBotClient(BotToken);

                User result = TelegramBotClient.GetMe().Result;

                if (result is null)
                {
                    throw new Exception("TelegramBotService GetMe失败");
                }

                TelegramBotClient.SendMessage(UserID, "ℹ️ XenUSDT 机器人启动");
            }
            catch (Exception ex)
            {
                throw new Exception("TelegramBotService 初始化失败", ex);
            }
        }

        public static void SendMessage(string text)
        {
            TelegramBotClient.SendMessage(UserID, text, Telegram.Bot.Types.Enums.ParseMode.Html, linkPreviewOptions: new LinkPreviewOptions { IsDisabled = true });
        }
    }
}
