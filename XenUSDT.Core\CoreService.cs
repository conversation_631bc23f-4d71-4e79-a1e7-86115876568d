﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XenUSDT.Core.Service;

namespace XenUSDT
{
    public class CoreService
    {
        public static AppConfigService AppConfigServiceHost { get; set; }

        public static FreeSQLService FreeSQLServiceHost { get; set; }

        public static TelegramBotService TelegramBotServiceHost { get; set; }

        public static void Init()
        {
            AppConfigServiceHost = new AppConfigService();
            FreeSQLServiceHost = new FreeSQLService();
            TelegramBotServiceHost = new TelegramBotService();
        }
    }
}
