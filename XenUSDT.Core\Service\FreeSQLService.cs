﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FreeSql;


namespace XenUSDT.Core.Service
{
    public class FreeSQLService
    {
        public static IFreeSql FreeSql { get; set; }

        public FreeSQLService()
        {
            try
            {
                Init();
            }
            catch (Exception ex)
            {
                throw new Exception("FreeSQLService 初始化失败", ex);
            }
        }

        public static void Init()
        {
            FreeSql = new FreeSqlBuilder()
               .UseConnectionString(DataType.MySql, CoreService.AppConfigServiceHost[AppConfigService.FreeSQLServiceConfigKey].ToString())
               .Build();
        }
    }
}
