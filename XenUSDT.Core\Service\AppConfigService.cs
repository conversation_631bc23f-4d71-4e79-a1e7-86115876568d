﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XenUSDT.Core.Service
{
    public class AppConfigService
    {
        public static string ConfigFilePath { get; set; } = "appsettings.json";
        public static string FreeSQLServiceConfigKey { get; set; } = "FreeSQL";

        public static JObject Config { get; set; }

        public object this[string key]
        {
            get
            {
                return GetConfigValue(key);
            }
        }

        public AppConfigService()
        {
            try
            {
                InitConfig();
            }
            catch (Exception ex)
            {
                throw new Exception("AppConfigService 初始化失败", ex);
            }
        }

        public static object GetConfigValue(string key)
        {
            if (Config == null)
            {
                throw new Exception("AppConfigService 配置未初始化");
            }
            if (Config.ContainsKey(key))
            {
                return Config[key];
            }
            else
            {
                return null;
            }
        }

        public static void InitConfig()
        {
            string basePath = AppContext.BaseDirectory;
            string jsonFilePath = Path.Combine(basePath, ConfigFilePath);

            if (!File.Exists(jsonFilePath))
            {
                return;
            }

            string jsonContent = File.ReadAllText(jsonFilePath);

            Config = JsonConvert.DeserializeObject<JObject>(jsonContent);
        }
    }
}
