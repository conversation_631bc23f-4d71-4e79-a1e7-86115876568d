﻿using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Linq;
using System.Text;
using TronNet.Crypto;
using XenUSDT.Core.DBModel;
using XenUSDT.Core.Service;
using XenUSDT.Web3Monitor.Service.TronMonitor;

namespace XenUSDT.Web3Monitor.Service
{
    public class TronMonitorService
    {
        public static int BlockNumber { get; set; }

        public static ConcurrentBag<MonitorAddress> MonitorAddressList { get; set; } = [];

        public static void UpdateMonitorAddressList(bool once = false)
        {
            while (true)
            {
                try
                {
                    var users = FreeSQLService.FreeSql.Select<MonitorAddress>().AsTable((t, o) => "monitor_address").ToList();

                    MonitorAddressList = [.. users];

                    //Console.WriteLine(@$"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 监控列表更新完成，监控地址数量: {MonitorAddressList.Count}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine(@$"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 监控列表更新失败: {ex.Message}");
                }

                if (!once)
                {
                    Thread.Sleep(TimeSpan.FromSeconds(10));
                }
                else
                {
                    return;
                }
            }
        }

        public static void SendBotMessage(MonitorAddress monitorAddress, string address, string type, string token, decimal transferAmount, int blockNum, string txId, string time)
        {
            string fromAddress = type == "in" ? address : monitorAddress.address;
            string toAddress = type == "in" ? monitorAddress.address : address;

            string msg = $@"
<b>备注</b>: #{monitorAddress.notes ?? $"监听地址{monitorAddress.id}"}
<b>金额</b>: <code>{(type == "in" ? "+" : "-")}{transferAmount} {token}</code>
<b>类型</b>: #{(type == "in" ? "转入" : "转出")}
<b>币种</b>: #{token}
<b>转出方</b>: <a href=""https://tronscan.org/#/address/{fromAddress}"">{fromAddress}</a>{(type == "out" ? " <b>←被监听</b>" : "")}
<b>转入方</b>: <a href=""https://tronscan.org/#/address/{toAddress}"">{toAddress}</a>{(type == "in" ? " <b>←被监听</b>" : "")}
<b>交易哈希</b>: <a href=""https://tronscan.org/#/transaction/{txId}"">{txId}</a>
<b>区块高度</b>: <a href=""https://tronscan.org/#/block/{blockNum}"">{blockNum}</a>
<b>时间</b>: <code>{time}</code>";

            Task.Run(() =>
            {
                TelegramBotService.SendMessage(msg);
            });
        }

        public static void Start()
        {
            Task.Run(() =>
            {
                UpdateMonitorAddressList(true);
            });

            Console.WriteLine(@$"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] TronMonitorService 已启动");

            string responseString;
            Stopwatch stopWatch;
            dynamic responseObject;

            responseString = TronMonitorServiceHelper.Get("https://api.trongrid.io/wallet/getnowblock");

            while (true)
            {
                responseObject = null;
                stopWatch = new Stopwatch();
                stopWatch.Start();

                try
                {
                    if (BlockNumber != 0)
                    {
                        responseString = TronMonitorServiceHelper.Post("https://api.trongrid.io/wallet/getblockbynum", JsonConvert.SerializeObject(new { num = BlockNumber + 1 }), Encoding.UTF8);
                    }

                    responseObject = JsonConvert.DeserializeObject<dynamic>(responseString);

                    if (responseObject == null) throw new ThreadSleepException();
                    if (responseObject.blockID == null) throw new ThreadSleepException();
                    if (responseObject.block_header == null) throw new ThreadSleepException();

                    BlockNumber = (int)responseObject.block_header.raw_data.number;

                    var blockHash = (string)responseObject.blockID;
                    var millisecondTimestamp = (long)responseObject.block_header.raw_data.timestamp;

                    //Console.WriteLine(@$"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 监控区块高度: {BlockNumber}");

                    if (responseObject.transactions == null || responseObject.transactions.Count == 0) continue;

                    foreach (var transaction in responseObject.transactions)
                    {
                        var ret = transaction.ret;
                        if (ret == null) continue;
                        if (ret.Count == 0) continue;
                        if (ret[0].contractRet == null || ret[0].contractRet != "SUCCESS") continue;

                        var rawData = transaction.raw_data;
                        if (rawData == null) continue;

                        var contracts = rawData.contract;
                        if (contracts == null) continue;
                        if (contracts.Count == 0) continue;

                        var contract = contracts[0];
                        if (contract == null) continue;

                        var parameter = contract.parameter;
                        if (parameter == null) continue;

                        var value = parameter.value;
                        if (value == null) continue;

                        var type = (string)contract.type;

                        if (type == "TriggerSmartContract")
                        {
                            if (value.contract_address != null && (string)value.contract_address == "41a614f803b6fd780986a42c78ec9c7f77e6ded13c")
                            {
                                var data = (string)value.data;

                                if (data[..8] == "a9059cbb")
                                {
                                    var txID = (string)transaction.txID;

                                    // USDT 转出地址
                                    var fromAddress = Base58Encoder.EncodeFromHex((string)value.owner_address, 0x41);
                                    // USDT 转入地址
                                    var toAddress = Base58Encoder.EncodeFromHex(((string)value.data).Substring(8, 64), 0x41);
                                    // 转账金额，long 类型
                                    var amount = Convert.ToInt64(((string)value.data).Substring(72, 64), 16);
                                    // 转化成  decimal 类型方便业务逻辑处理
                                    var transferAmount = amount / new decimal(1000000);

                                    foreach (var item in MonitorAddressList.Where(i => i.address == fromAddress || i.address == toAddress))
                                    {
                                        // 如果是转出地址      
                                        if (item.address == fromAddress)
                                        {
                                            Console.WriteLine(@$"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 地址转出USDT: {fromAddress} 金额: {transferAmount} 交易哈希: {txID}");
                                            SendBotMessage(item, toAddress, "out", "USDT", transferAmount, BlockNumber, txID, DateTimeOffset.FromUnixTimeMilliseconds(millisecondTimestamp).DateTime.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss"));
                                        }
                                        // 如果是转入地址
                                        if (item.address == toAddress)
                                        {
                                            Console.WriteLine(@$"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 地址转入USDT: {toAddress} 金额: {transferAmount} 交易哈希: {txID}");
                                            SendBotMessage(item, fromAddress, "in", "USDT", transferAmount, BlockNumber, txID, DateTimeOffset.FromUnixTimeMilliseconds(millisecondTimestamp).DateTime.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss"));
                                        }
                                    }
                                }
                            }
                        }
                        else if (type == "TransferContract")
                        {
                            // TRX转账
                            var txID = (string)transaction.txID;
                            var fromAddress = Base58Encoder.EncodeFromHex((string)value.owner_address, 0x41);
                            var toAddress = Base58Encoder.EncodeFromHex((string)value.to_address, 0x41);
                            var amount = (long)value.amount / new decimal(1000000); // TRX精度是6位小数

                            foreach (var item in MonitorAddressList.Where(i => i.address == fromAddress || i.address == toAddress))
                            {
                                // 如果是转出地址      
                                if (item.address == fromAddress)
                                {
                                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 地址转出TRX: {fromAddress} 金额: {amount} 交易哈希: {txID}");
                                    SendBotMessage(item, toAddress, "out", "TRX", amount, BlockNumber, txID, DateTimeOffset.FromUnixTimeMilliseconds(millisecondTimestamp).DateTime.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss"));
                                }
                                // 如果是转入地址
                                if (item.address == toAddress)
                                {
                                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 地址转入TRX: {toAddress} 金额: {amount} 交易哈希: {txID}");
                                    SendBotMessage(item, fromAddress, "in", "TRX", amount, BlockNumber, txID, DateTimeOffset.FromUnixTimeMilliseconds(millisecondTimestamp).DateTime.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss"));
                                }
                            }
                        }
                    }

                    if (stopWatch.ElapsedMilliseconds >= 2500) continue;
                    Thread.Sleep((int)(2500 - stopWatch.ElapsedMilliseconds));
                }
                catch (ThreadSleepException)
                {
                    //Console.WriteLine(@$"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 等待出块: {BlockNumber + 1}");

                    if (stopWatch.ElapsedMilliseconds >= 1000) continue;
                    Thread.Sleep((int)(1000 - stopWatch.ElapsedMilliseconds));
                }
                catch (Exception exception)
                {
                    Console.WriteLine(@$"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}]监控异常: {exception}");

                    if (stopWatch.ElapsedMilliseconds >= 1000) continue;
                    Thread.Sleep((int)(1000 - stopWatch.ElapsedMilliseconds));
                }
            }
        }
    }
}
