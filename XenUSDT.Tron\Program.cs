﻿using XenUSDT.Tron.Service;

namespace XenUSDT.Tron
{
    internal class Program
    {
        static void Main(string[] args)
        {
            CoreService.Init();

            Console.WriteLine("XenUSDT.Tron 地址余额查询服务启动");

            // 查询并更新所有地址的余额和多签状态
            TronAddressBalanceService.QueryAndUpdateAddressBalances();

            Console.WriteLine("查询完成，按任意键退出...");
            Console.ReadKey();
        }
    }
}
