﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace XenUSDT.Web3Monitor.Service.TronMonitor
{
    public class TronMonitorServiceHelper
    {
        public static string API_KEY = "c3c8adf1-6221-4e65-ba3a-012a07c45fff";

        public static string Get(string url, int timeout = 12000)
        {
            var resp = Get((HttpWebRequest)WebRequest.Create(url), timeout);

            using var s = resp.GetResponseStream();
            using var sr = new StreamReader(s);

            return sr.ReadToEnd();
        }

        private static HttpWebResponse Get(HttpWebRequest req, int timeout = 12000)
        {
            req.Method = "GET";
            req.ContentType = "application/json";
            req.Timeout = timeout;
            req.Accept = "application/json";
            req.Headers.Set("TRON-PRO-API-KEY", API_KEY);

            return (HttpWebResponse)req.GetResponse();
        }

        public static string Post(string url, string requestBody, Encoding encoding, int timeout = 12000)
        {
            var resp = Post((HttpWebRequest)WebRequest.Create(url), requestBody, encoding, timeout);

            using var s = resp.GetResponseStream();
            using var sr = new StreamReader(s);

            return sr.ReadToEnd();
        }

        private static HttpWebResponse Post(HttpWebRequest req, string requestBody, Encoding encoding, int timeout = 12000)
        {
            var bs = encoding.GetBytes(requestBody);

            req.Method = "POST";
            req.ContentType = "application/json";
            req.ContentLength = bs.Length;
            req.Timeout = timeout;
            req.Accept = "application/json";
            req.Headers.Set("TRON-PRO-API-KEY", API_KEY);

            using (var s = req.GetRequestStream())
            {
                s.Write(bs, 0, bs.Length);
            }

            return (HttpWebResponse)req.GetResponse();
        }
    }
}
